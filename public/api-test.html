<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Format Convert API 测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #2563eb;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #374151;
        }
        input, textarea, select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.2s;
        }
        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #2563eb;
        }
        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        button:hover {
            background: #1d4ed8;
        }
        button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        .result {
            margin-top: 30px;
            padding: 20px;
            background: #f8fafc;
            border-radius: 8px;
            border-left: 4px solid #2563eb;
        }
        .error {
            border-left-color: #dc2626;
            background: #fef2f2;
            color: #dc2626;
        }
        .success {
            border-left-color: #059669;
            background: #f0fdf4;
        }
        pre {
            background: #1f2937;
            color: #f9fafb;
            padding: 16px;
            border-radius: 8px;
            overflow-x: auto;
            font-size: 13px;
            line-height: 1.5;
        }
        .examples {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .example {
            background: #f3f4f6;
            padding: 15px;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .example:hover {
            background: #e5e7eb;
        }
        .example-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 5px;
        }
        .example-value {
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 13px;
            color: #6b7280;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #2563eb;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🕐 Enhanced Format Convert API 测试</h1>
        
        <form id="testForm">
            <div class="form-group">
                <label for="input">输入时间值:</label>
                <input type="text" id="input" placeholder="例如: 1705315845, 2024-01-15T10:30:45Z, 2024年1月15日" required>
            </div>

            <div class="form-group">
                <label>常用示例 (点击使用):</label>
                <div class="examples">
                    <div class="example" onclick="setInput('1705315845')">
                        <div class="example-title">Unix 时间戳</div>
                        <div class="example-value">1705315845</div>
                    </div>
                    <div class="example" onclick="setInput('2024-01-15T10:30:45Z')">
                        <div class="example-title">ISO 8601</div>
                        <div class="example-value">2024-01-15T10:30:45Z</div>
                    </div>
                    <div class="example" onclick="setInput('2024年1月15日 10时30分45秒')">
                        <div class="example-title">中文格式</div>
                        <div class="example-value">2024年1月15日 10时30分45秒</div>
                    </div>
                    <div class="example" onclick="setInput('01/15/2024 10:30:45')">
                        <div class="example-title">美式格式</div>
                        <div class="example-value">01/15/2024 10:30:45</div>
                    </div>
                    <div class="example" onclick="setInput('2 hours ago')">
                        <div class="example-title">相对时间</div>
                        <div class="example-value">2 hours ago</div>
                    </div>
                    <div class="example" onclick="setInput('45310.4379861111')">
                        <div class="example-title">Excel 序列号</div>
                        <div class="example-value">45310.4379861111</div>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="outputFormats">输出格式 (多选，Ctrl+点击):</label>
                <select id="outputFormats" multiple size="8">
                    <option value="iso8601" selected>ISO 8601</option>
                    <option value="unix_seconds" selected>Unix 时间戳 (秒)</option>
                    <option value="unix_milliseconds">Unix 时间戳 (毫秒)</option>
                    <option value="chinese_traditional">中文传统格式</option>
                    <option value="us_date_slash">美式日期</option>
                    <option value="eu_date_dot">欧式日期</option>
                    <option value="mysql_datetime">MySQL 格式</option>
                    <option value="relative_time">相对时间</option>
                    <option value="excel_date">Excel 序列号</option>
                    <option value="rfc3339">RFC 3339</option>
                    <option value="log_format">日志格式</option>
                    <option value="japanese_era">日本年号</option>
                </select>
            </div>

            <div class="form-group">
                <label for="timezone">时区 (可选):</label>
                <select id="timezone">
                    <option value="">自动检测</option>
                    <option value="UTC">UTC</option>
                    <option value="Asia/Shanghai">Asia/Shanghai (北京时间)</option>
                    <option value="America/New_York">America/New_York</option>
                    <option value="Europe/London">Europe/London</option>
                    <option value="Asia/Tokyo">Asia/Tokyo</option>
                </select>
            </div>

            <div class="form-group">
                <label>
                    <input type="checkbox" id="autoDetect" checked> 启用自动格式检测
                </label>
            </div>

            <div class="form-group">
                <label>
                    <input type="checkbox" id="includeAlternatives"> 包含替代格式建议
                </label>
            </div>

            <div class="form-group">
                <label>
                    <input type="checkbox" id="includeMetadata" checked> 包含详细元数据
                </label>
            </div>

            <button type="submit" id="submitBtn">
                <span id="loadingSpinner" class="loading" style="display: none;"></span>
                转换格式
            </button>
        </form>

        <div id="result" class="result" style="display: none;">
            <h3>转换结果:</h3>
            <pre id="resultContent"></pre>
        </div>
    </div>

    <script>
        function setInput(value) {
            document.getElementById('input').value = value;
        }

        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const loadingSpinner = document.getElementById('loadingSpinner');
            const resultDiv = document.getElementById('result');
            const resultContent = document.getElementById('resultContent');
            
            // 显示加载状态
            submitBtn.disabled = true;
            loadingSpinner.style.display = 'inline-block';
            
            // 获取表单数据
            const input = document.getElementById('input').value;
            const outputFormatsSelect = document.getElementById('outputFormats');
            const outputFormats = Array.from(outputFormatsSelect.selectedOptions).map(option => option.value);
            const timezone = document.getElementById('timezone').value;
            const autoDetect = document.getElementById('autoDetect').checked;
            const includeAlternatives = document.getElementById('includeAlternatives').checked;
            const includeMetadata = document.getElementById('includeMetadata').checked;
            
            if (outputFormats.length === 0) {
                alert('请至少选择一种输出格式');
                submitBtn.disabled = false;
                loadingSpinner.style.display = 'none';
                return;
            }
            
            const requestData = {
                input: input,
                outputFormats: outputFormats,
                options: {
                    autoDetect: autoDetect,
                    includeAlternatives: includeAlternatives,
                    includeMetadata: includeMetadata
                }
            };
            
            if (timezone) {
                requestData.timezone = timezone;
            }
            
            try {
                const response = await fetch('/api/enhanced-format-convert', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });
                
                const result = await response.json();
                
                // 显示结果
                resultDiv.style.display = 'block';
                resultDiv.className = response.ok ? 'result success' : 'result error';
                resultContent.textContent = JSON.stringify(result, null, 2);
                
                // 滚动到结果
                resultDiv.scrollIntoView({ behavior: 'smooth' });
                
            } catch (error) {
                resultDiv.style.display = 'block';
                resultDiv.className = 'result error';
                resultContent.textContent = `请求失败: ${error.message}`;
            } finally {
                // 恢复按钮状态
                submitBtn.disabled = false;
                loadingSpinner.style.display = 'none';
            }
        });
    </script>
</body>
</html>
