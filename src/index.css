/* Google Fonts now loaded via preload in index.html */
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light theme colors */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    /* Dark theme colors */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 68%;
  }

  * {
    @apply border-border;
  }

  body {
    font-family:
      'Inter',
      -apple-system,
      BlinkMacSystemFont,
      'Segoe UI',
      'Roboto',
      sans-serif;
    @apply bg-background text-foreground antialiased;
    font-display: swap; /* Ensure text remains visible during font loading */
  }

  /* Custom scrollbar for dark mode */
  ::-webkit-scrollbar {
    width: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-transparent;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-slate-300 dark:bg-slate-600 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-slate-400 dark:bg-slate-500;
  }
}

@layer components {
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-slate-900;
  }

  /* Mobile-specific optimizations */
  .mobile-input {
    font-size: 16px; /* Prevent iOS zoom */
    -webkit-appearance: none;
    -webkit-tap-highlight-color: transparent;
  }

  .mobile-button {
    min-height: 44px;
    min-width: 44px;
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
  }

  .mobile-touch-target {
    min-height: 44px;
    min-width: 44px;
    touch-action: manipulation;
  }

  /* Improve touch feedback */
  .touch-feedback {
    transition:
      transform 0.1s ease-in-out,
      background-color 0.15s ease-in-out;
  }

  .touch-feedback:active {
    transform: scale(0.98);
  }

  /* Optimize for mobile viewport */
  @media (max-width: 640px) {
    .mobile-optimized {
      padding-left: 1rem;
      padding-right: 1rem;
    }

    .mobile-spacing {
      margin-bottom: 1rem;
    }

    .mobile-text {
      font-size: 16px;
      line-height: 1.5;
    }
  }

  /* Prevent zoom on input focus for iOS */
  @media screen and (-webkit-min-device-pixel-ratio: 0) {
    select,
    textarea,
    input[type='text'],
    input[type='password'],
    input[type='datetime'],
    input[type='datetime-local'],
    input[type='date'],
    input[type='month'],
    input[type='time'],
    input[type='week'],
    input[type='number'],
    input[type='email'],
    input[type='url'],
    input[type='search'],
    input[type='tel'],
    input[type='color'] {
      font-size: 16px;
    }
  }
}
