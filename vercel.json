{"buildCommand": "npm run build", "framework": "vite", "rewrites": [{"source": "/api/enhanced-admin/(.*)", "destination": "/api/enhanced-admin"}, {"source": "/api/enhanced-health", "destination": "/api/enhanced-health"}, {"source": "/api/enhanced-format-convert", "destination": "/api/enhanced-format-convert"}, {"source": "/api/optimized-batch-convert", "destination": "/api/optimized-batch-convert"}, {"source": "/api/batch-convert", "destination": "/api/v1/batch-convert"}, {"source": "/api/enhanced-batch", "destination": "/api/v1/enhanced-batch"}, {"source": "/api/formats", "destination": "/api/v1/formats"}, {"source": "/api/timezone-convert", "destination": "/api/v1/timezone-convert"}, {"source": "/api/timezone-difference", "destination": "/api/v1/timezone-difference"}, {"source": "/api/timezone-info", "destination": "/api/v1/timezone-info"}, {"source": "/api/timezone", "destination": "/api/v1/timezone"}, {"source": "/api/visualization", "destination": "/api/v1/visualization"}, {"source": "/api/simple-convert", "destination": "/api/v1/simple-convert"}, {"source": "/api/simple-health", "destination": "/api/v1/simple-health"}, {"source": "/api/standalone-convert", "destination": "/api/v1/standalone-convert"}, {"source": "/api/standalone-health", "destination": "/api/v1/standalone-health"}, {"source": "/api/working-batch", "destination": "/api/v1/working-batch"}, {"source": "/api/working-convert", "destination": "/api/v1/working-convert"}, {"source": "/api/working-health", "destination": "/api/v1/working-health"}, {"source": "/api/batch", "destination": "/api/v1/batch"}, {"source": "/api/redis-admin", "destination": "/api/admin/redis-admin"}, {"source": "/api/redis-config", "destination": "/api/admin/redis-config"}, {"source": "/api/metrics", "destination": "/api/admin/metrics"}, {"source": "/api/test", "destination": "/api/admin/test"}, {"source": "/api/docs", "destination": "/api/docs/docs"}, {"source": "/api/swagger", "destination": "/api/docs/swagger"}, {"source": "/api/openapi", "destination": "/api/docs/openapi"}, {"source": "/api/simple-api", "destination": "/api/docs/simple-api"}, {"source": "/(.*)", "destination": "/index.html"}], "functions": {"api/**": {"maxDuration": 20, "memory": 1024}}, "outputDirectory": "dist", "installCommand": "npm ci", "headers": [{"source": "/api/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "public, max-age=0, s-maxage=86400, stale-while-revalidate=60"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization, X-API-Key"}]}], "env": {"NODE_ENV": "production", "RATE_LIMITING_ENABLED": "true", "CACHING_ENABLED": "true", "METRICS_ENABLED": "true", "LOG_LEVEL": "info"}, "crons": [{"path": "/api/health", "schedule": "0 0 * * *"}]}